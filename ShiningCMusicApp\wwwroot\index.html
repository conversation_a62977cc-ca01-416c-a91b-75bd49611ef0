<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>ShiningCMusicApp</title>
    <base href="/" />
    <link rel="stylesheet" href="css/bootstrap/bootstrap.min.css" />
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.0/font/bootstrap-icons.css" />
    <link rel="stylesheet" href="css/app.css" />
    <link rel="icon" type="image/png" href="favicon.png" />
    <link href="ShiningCMusicApp.styles.css" rel="stylesheet" />
    <link href="_content/Syncfusion.Blazor.Themes/bootstrap5.css" rel="stylesheet" />
    <script src="_content/Syncfusion.Blazor.Core/scripts/syncfusion-blazor.min.js" type="text/javascript"></script>
</head>

<body>
    <div id="app">
        <svg class="loading-progress">
            <circle r="40%" cx="50%" cy="50%" />
            <circle r="40%" cx="50%" cy="50%" />
        </svg>
        <div class="loading-progress-text"></div>
    </div>

    <div id="blazor-error-ui">
        An unhandled error has occurred.
        <a href="" class="reload">Reload</a>
        <a class="dismiss">🗙</a>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Simple mobile menu functions
        function openMobileMenu() {
            console.log('Opening mobile menu...');
            const menu = document.getElementById('mobileMenu');
            const overlay = document.getElementById('mobileMenuOverlay');

            if (menu && overlay) {
                menu.classList.add('show');
                overlay.classList.add('show');
                document.body.style.overflow = 'hidden'; // Prevent scrolling
                console.log('Mobile menu opened');
            } else {
                console.error('Menu or overlay not found');
            }
        }

        function closeMobileMenu() {
            console.log('Closing mobile menu...');
            const menu = document.getElementById('mobileMenu');
            const overlay = document.getElementById('mobileMenuOverlay');

            if (menu && overlay) {
                menu.classList.remove('show');
                overlay.classList.remove('show');
                document.body.style.overflow = ''; // Restore scrolling
                console.log('Mobile menu closed');
            }
        }

        // Test function
        document.addEventListener('DOMContentLoaded', function() {
            console.log('DOM loaded, mobile menu ready');
            const menuButton = document.querySelector('.mobile-menu-btn');
            const menu = document.getElementById('mobileMenu');
            console.log('Menu button found:', !!menuButton);
            console.log('Menu element found:', !!menu);
        });

        // Window resize handler for Blazor MainLayout
        let blazorMainLayoutRef = null;
        let resizeTimeout = null;
        let documentClickRef = null;

        window.addResizeListener = function(dotNetObjectReference) {
            blazorMainLayoutRef = dotNetObjectReference;
            console.log('Resize listener added');
        };

        window.addEventListener('resize', function() {
            if (blazorMainLayoutRef) {
                // Debounce resize events to avoid too many calls
                clearTimeout(resizeTimeout);
                resizeTimeout = setTimeout(function() {
                    console.log('Window resized, calling Blazor method');
                    blazorMainLayoutRef.invokeMethodAsync('OnWindowResize');
                }, 250); // Wait 250ms after resize stops
            }
        });

        // Document click handler for mobile sidebar
        window.addDocumentClickListener = function(dotNetObjectReference) {
            documentClickRef = dotNetObjectReference;

            // Remove existing listener if any
            if (window.documentClickHandler) {
                document.removeEventListener('click', window.documentClickHandler);
            }

            // Add new listener
            window.documentClickHandler = function(event) {
                // Check if click is outside sidebar and overlay
                const sidebar = document.querySelector('#mainSidebar');
                const overlay = document.querySelector('.e-sidebar-overlay');

                if (sidebar && !sidebar.contains(event.target)) {
                    // Clicked outside sidebar, close it
                    documentClickRef.invokeMethodAsync('OnDocumentClick');
                }
            };

            document.addEventListener('click', window.documentClickHandler);
            console.log('Document click listener added for mobile sidebar');
        };

        window.removeDocumentClickListener = function() {
            if (window.documentClickHandler) {
                document.removeEventListener('click', window.documentClickHandler);
                window.documentClickHandler = null;
                console.log('Document click listener removed');
            }
            documentClickRef = null;
        };
    </script>
    <script src="_framework/blazor.webassembly.js"></script>
</body>

</html>
