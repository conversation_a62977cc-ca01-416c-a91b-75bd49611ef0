﻿@inherits LayoutComponentBase
@using ShiningCMusicApp.Services
@using Syncfusion.Blazor.Navigations
@inject CustomAuthenticationStateProvider AuthStateProvider
@inject NavigationManager Navigation
@inject IJSRuntime JSRuntime
@implements IDisposable

<div class="main-layout">
    <!-- Syncfusion Sidebar -->
    <SfSidebar @ref="SidebarObj"
               ID="mainSidebar"
               Width="250px"
               Type="SidebarType.Auto"
               Position="SidebarPosition.Left"
               @bind-IsOpen="@IsSidebarOpen"
               EnableDock="false"
               MediaQuery="(min-width: 992px)"
               EnablePersistence="true">
        <ChildContent>
            <div class="sidebar-content">
                <!-- Sidebar Header -->
                <div class="sidebar-header">
                    <div class="d-flex align-items-center p-3">
                        <i class="fas fa-music me-2 text-white"></i>
                        <span class="text-white fw-bold">Shining C Music</span>
                    </div>
                </div>

                <!-- Navigation Menu -->
                <div class="sidebar-nav">
                    <AuthorizeView Roles="Administrator">
                        <Authorized>
                            <div class="nav-item">
                                <a href="/" class="nav-link @GetActiveClass("/")" @onclick="@(() => NavigateAndClose("/"))">
                                    <i class="bi bi-house-door-fill me-2"></i> Home
                                </a>
                            </div>
                            <div class="nav-item">
                                <a href="/lessons" class="nav-link @GetActiveClass("/lessons")" @onclick="@(() => NavigateAndClose("/lessons"))">
                                    <i class="bi bi-calendar-event me-2"></i> Lessons
                                </a>
                            </div>
                            <div class="nav-item">
                                <a href="/tutors" class="nav-link @GetActiveClass("/tutors")" @onclick="@(() => NavigateAndClose("/tutors"))">
                                    <i class="bi bi-person-fill me-2"></i> Tutors
                                </a>
                            </div>
                            <div class="nav-item">
                                <a href="/students" class="nav-link @GetActiveClass("/students")" @onclick="@(() => NavigateAndClose("/students"))">
                                    <i class="bi bi-people-fill me-2"></i> Students
                                </a>
                            </div>
                            <div class="nav-item">
                                <a href="/admin" class="nav-link @GetActiveClass("/admin")" @onclick="@(() => NavigateAndClose("/admin"))">
                                    <i class="bi bi-gear-fill me-2"></i> Admin
                                </a>
                            </div>
                        </Authorized>
                    </AuthorizeView>
                    <AuthorizeView Roles="Tutor,Student">
                        <Authorized>
                            <div class="nav-item">
                                <a href="/lessons" class="nav-link @GetActiveClass("/lessons")" @onclick="@(() => NavigateAndClose("/lessons"))">
                                    <i class="bi bi-calendar-event me-2"></i> My Lessons
                                </a>
                            </div>
                        </Authorized>
                    </AuthorizeView>
                </div>
            </div>
        </ChildContent>
    </SfSidebar>

    <!-- Main Content Area -->
    <div class="main-content">
        <!-- Top Bar -->
        <div class="top-bar">
            <!-- Mobile Menu Toggle - Only visible on mobile -->
            <button class="menu-toggle d-lg-none" @onclick="ToggleSidebar">
                <i class="fas fa-bars"></i>
            </button>

            <!-- User Info -->
            <div class="user-info ms-auto">
                <AuthorizeView>
                    <Authorized>
                        <div class="d-flex align-items-center">
                            <span class="text-dark me-3">Welcome, @context.User.Identity?.Name</span>
                            <button class="btn btn-outline-primary btn-sm" @onclick="Logout">
                                <i class="fas fa-sign-out-alt"></i> Logout
                            </button>
                        </div>
                    </Authorized>
                </AuthorizeView>
            </div>
        </div>

        <!-- Page Content -->
        <div class="page-content">
            @Body
        </div>
    </div>
</div>

@code {
    private SfSidebar? SidebarObj;
    private bool IsDesktop = true;
    private bool IsSidebarOpen = true;

    protected override void OnInitialized()
    {
        // Initialize with defaults - Syncfusion will handle responsive behavior
        IsDesktop = true;
        IsSidebarOpen = true; // Start open, MediaQuery will control responsive behavior
        Console.WriteLine("MainLayout initialized - Syncfusion will handle responsive behavior");
    }

    protected override async Task OnAfterRenderAsync(bool firstRender)
    {
        if (firstRender)
        {
            try
            {
                await CheckScreenSize();

                // Add window resize listener
                await JSRuntime.InvokeVoidAsync("addResizeListener", DotNetObjectReference.Create(this));
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error in OnAfterRenderAsync: {ex.Message}");
                // Use desktop defaults if everything fails
                IsDesktop = true;
                IsSidebarOpen = true;
            }
            finally
            {
                StateHasChanged();
            }
        }
    }

    [JSInvokable]
    public async Task OnWindowResize()
    {
        try
        {
            await CheckScreenSize();
            StateHasChanged();
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error in OnWindowResize: {ex.Message}");
        }
    }

    private async Task CheckScreenSize()
    {
        try
        {
            var width = await JSRuntime.InvokeAsync<int>("eval", "window.innerWidth");
            var wasDesktop = IsDesktop;
            IsDesktop = width >= 992; // Bootstrap lg breakpoint

            Console.WriteLine($"Screen width: {width}, IsDesktop: {IsDesktop}");

            // Let Syncfusion handle the responsive behavior automatically with SidebarType.Auto
            // We only need to manage the open/close state
            if (IsDesktop)
            {
                // On desktop, ensure sidebar is always open
                IsSidebarOpen = true;
                Console.WriteLine("Desktop mode: Sidebar always open, Syncfusion Auto handles responsive behavior");
            }
            else
            {
                // On mobile, let user control open/close state
                Console.WriteLine("Mobile mode: Sidebar controlled by user, Syncfusion Auto handles responsive behavior");
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error checking screen size: {ex.Message}");
            // Fallback to desktop defaults if JS fails
            IsDesktop = true;
            IsSidebarOpen = true;
        }
    }



    private void ToggleSidebar()
    {
        // Only allow toggle on mobile - desktop sidebar is always open
        if (!IsDesktop)
        {
            IsSidebarOpen = !IsSidebarOpen;
            Console.WriteLine($"Mobile sidebar toggled: {IsSidebarOpen}");
        }
        else
        {
            Console.WriteLine("Desktop sidebar toggle ignored - always open");
        }
    }

    private void NavigateAndClose(string url)
    {
        Navigation.NavigateTo(url);

        // Close sidebar on mobile after navigation - desktop stays open
        if (!IsDesktop)
        {
            IsSidebarOpen = false;
            Console.WriteLine("Mobile navigation: Sidebar closed");
        }
        else
        {
            Console.WriteLine("Desktop navigation: Sidebar remains open");
        }
    }

    private string GetActiveClass(string path)
    {
        var currentPath = Navigation.ToBaseRelativePath(Navigation.Uri);
        if (string.IsNullOrEmpty(currentPath) && path == "/")
            return "active";

        return currentPath.StartsWith(path.TrimStart('/')) ? "active" : "";
    }

    private async Task Logout()
    {
        await AuthStateProvider.LogoutAsync();
        Navigation.NavigateTo("/login");
    }

    public void Dispose()
    {
        // Clean up the resize listener reference
        try
        {
            JSRuntime.InvokeVoidAsync("eval", "blazorMainLayoutRef = null;");
        }
        catch
        {
            // Ignore errors during disposal
        }
    }
}
