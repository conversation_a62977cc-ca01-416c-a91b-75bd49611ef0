@page "/"
@attribute [Authorize(Roles = "Administrator")]

<PageTitle>Shining C Music School</PageTitle>

<div class="container-lg py-5">
    <div class="row mb-5">
        <div class="col-12 text-center">
            <h1 class="display-4 mb-3">🎵 Shining C Music School</h1>
            <p class="lead text-muted">Welcome to our music lesson management system</p>
        </div>
    </div>

    <div class="row justify-content-center mb-5">
        <div class="col-lg-8 col-xl-6">
            <div class="card shadow-sm">
                <div class="card-body text-center p-4">
                    <h5 class="card-title mb-3">Lesson Time Table</h5>
                    <p class="card-text mb-4">View and manage music lesson schedules for tutors and students.</p>
                    <a href="/lessons" class="btn btn-primary btn-lg px-4">
                        <i class="fas fa-calendar-alt me-2"></i> Open Scheduler
                    </a>
                </div>
            </div>
        </div>
    </div>

    <div class="row g-4">
        <div class="col-md-4 d-flex">
            <div class="card shadow-sm flex-fill">
                <div class="card-body text-center d-flex flex-column p-4">
                    <h6 class="card-title mb-3">👨‍🏫 Manage Tutors</h6>
                    <p class="card-text flex-grow-1 mb-4">Add, edit, and manage tutor information</p>
                    <a href="/tutors" class="btn btn-outline-primary mt-auto">
                        <i class="fas fa-user me-2"></i> Manage Tutors
                    </a>
                </div>
            </div>
        </div>
        <div class="col-md-4 d-flex">
            <div class="card shadow-sm flex-fill">
                <div class="card-body text-center d-flex flex-column p-4">
                    <h6 class="card-title mb-3">👨‍🎓 Manage Students</h6>
                    <p class="card-text flex-grow-1 mb-4">Add, edit, and manage student information</p>
                    <a href="/students" class="btn btn-outline-primary mt-auto">
                        <i class="fas fa-users me-2"></i> Manage Students
                    </a>
                </div>
            </div>
        </div>
        <div class="col-md-4 d-flex">
            <div class="card shadow-sm flex-fill">
                <div class="card-body text-center d-flex flex-column p-4">
                    <h6 class="card-title mb-3">⚙️ Admin Management</h6>
                    <p class="card-text flex-grow-1 mb-4">Manage system configuration including subjects and locations</p>
                    <a href="/admin" class="btn btn-outline-primary mt-auto">
                        <i class="fas fa-cog me-2"></i> Admin Panel
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

@code {
    // Authentication is handled by [Authorize] attribute
}
