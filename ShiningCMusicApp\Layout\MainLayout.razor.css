/* Main Layout Structure */
.main-layout {
    height: 100vh;
    overflow: hidden;
}

/* Syncfusion Sidebar Customization */
::deep .e-sidebar {
    background-image: linear-gradient(180deg, rgb(5, 39, 103) 0%, #3a0647 70%);
    z-index: 1000;
}

::deep .e-sidebar .e-sidebar-overlay {
    z-index: 999;
}

/* Complete backdrop control - overrides all Syncfusion backdrop behavior */
/* Desktop: NO backdrop ever */
@media (min-width: 992px) {
    ::deep .e-sidebar-overlay,
    ::deep .e-overlay,
    ::deep .e-backdrop,
    ::deep .e-sidebar .e-sidebar-overlay {
        display: none !important;
        background: transparent !important;
        opacity: 0 !important;
        pointer-events: none !important;
        visibility: hidden !important;
        z-index: -1 !important;
        transition: none !important;
        position: absolute !important;
        top: -9999px !important;
        left: -9999px !important;
    }

    /* Force sidebar to Push type behavior on desktop */
    ::deep .e-sidebar {
        position: fixed !important;
        top: 0 !important;
        left: 0 !important;
        height: 100% !important;
        transform: translateX(0) !important;
        transition: none !important;
    }

    /* Push main content on desktop when sidebar is open */
    ::deep .e-sidebar.e-open ~ .main-content,
    .main-content {
        margin-left: 250px !important;
        transition: margin-left 0.3s ease !important;
        position: relative !important;
        z-index: 1 !important;
    }

    /* When sidebar is closed on desktop, reset margin */
    ::deep .e-sidebar:not(.e-open) ~ .main-content {
        margin-left: 0 !important;
    }
}

/* Sidebar Content */
.sidebar-content {
    height: 100%;
    display: flex;
    flex-direction: column;
    font-family: 'Helvetica Neue', Helvetica, Arial, sans-serif;
}

.sidebar-header {
    background-color: rgba(0,0,0,0.4);
    border-bottom: 1px solid rgba(255,255,255,0.1);
    flex-shrink: 0;
    font-family: 'Helvetica Neue', Helvetica, Arial, sans-serif;
}

.sidebar-nav {
    flex: 1;
    overflow-y: auto;
    padding: 1rem 0;
}

/* Navigation Items */
.nav-item {
    margin-bottom: 0.25rem;
}

.nav-link {
    display: flex;
    align-items: center;
    padding: 0.75rem 1.5rem;
    color: rgba(255,255,255,0.9);
    text-decoration: none;
    transition: all 0.2s ease;
    border-radius: 0;
    font-family: 'Helvetica Neue', Helvetica, Arial, sans-serif;
    font-size: 0.95rem;
    font-weight: 400;
}

.nav-link:hover {
    background-color: rgba(255,255,255,0.1);
    color: white;
    text-decoration: none;
}

.nav-link.active {
    background-color: rgba(255,255,255,0.2);
    color: white;
    font-weight: 500;
}

.nav-link i {
    width: 20px;
    text-align: center;
}

/* Main Content Area */
.main-content {
    height: 100vh;
    display: flex;
    flex-direction: column;
    transition: margin-left 0.3s ease;
}

/* Top Bar */
.top-bar {
    background-color: #f8f9fa;
    border-bottom: 1px solid #dee2e6;
    height: 60px;
    display: flex;
    align-items: center;
    padding: 0 1rem;
    flex-shrink: 0;
    z-index: 100;
}

.menu-toggle {
    background: transparent;
    border: none;
    font-size: 1.8rem;
    color: #495057;
    cursor: pointer;
    padding: 0.75rem;
    border-radius: 6px;
    transition: all 0.2s ease;
    display: flex !important;
    align-items: center;
    justify-content: center;
    min-width: 44px;
    height: 44px;
    opacity: 0.8;
    font-family: Arial, sans-serif;
    line-height: 1;
}

.menu-toggle:hover {
    background-color: rgba(0,0,0,0.08);
    color: #212529;
    opacity: 1;
    transform: scale(1.05);
}

.menu-toggle:focus {
    outline: none;
    box-shadow: 0 0 0 2px rgba(0,123,255,0.25);
    opacity: 1;
}

.menu-toggle:active {
    transform: scale(0.95);
    background-color: rgba(0,0,0,0.12);
}

/* Sidebar Toggle Button - Removed since desktop sidebar is always open */

.user-info {
    display: flex;
    align-items: center;
}

/* Page Content */
.page-content {
    flex: 1;
    overflow-y: auto;
    padding: 1.5rem;
}

/* Desktop Styles */
@media (min-width: 992px) {
    .main-content {
        margin-left: 0; /* Let Syncfusion handle the margin with Push type */
        transition: margin-left 0.3s ease;
    }

    /* Hide mobile toggle on desktop */
    .menu-toggle.d-lg-none {
        display: none !important;
    }

    /* When sidebar is open on desktop with Push type, Syncfusion will handle the margin */
    ::deep .e-sidebar {
        position: fixed !important;
        z-index: 1000;
    }

    /* Force no backdrop on desktop - immediate CSS override */
    ::deep .e-sidebar-overlay {
        display: none !important;
        background-color: transparent !important;
        opacity: 0 !important;
        pointer-events: none !important;
        visibility: hidden !important;
        z-index: -1 !important;
    }

    /* Also target any backdrop that might be created dynamically */
    ::deep .e-overlay {
        display: none !important;
        background-color: transparent !important;
        opacity: 0 !important;
        pointer-events: none !important;
        visibility: hidden !important;
    }
}

/* Mobile Styles - Force Over type behavior */
@media (max-width: 991.98px) {
    /* Reset main content - no margin on mobile (Over behavior) */
    .main-content {
        margin-left: 0 !important;
        transform: none !important;
    }

    .page-content {
        padding: 1rem;
    }

    /* Mobile toggle button - always visible */
    .menu-toggle.d-lg-none {
        display: flex !important;
    }

    /* Force Over type: sidebar slides over content, doesn't push it */
    ::deep .e-sidebar {
        position: fixed !important;
        top: 0 !important;
        left: -250px !important; /* Start hidden off-screen */
        width: 250px !important;
        height: 100% !important;
        z-index: 1001 !important;
        transition: left 0.3s ease !important;
        transform: none !important;
    }

    /* When open, slide in from left (Over behavior) */
    ::deep .e-sidebar.e-open {
        left: 0 !important;
    }

    /* Ensure main content never moves on mobile */
    ::deep .e-sidebar.e-open ~ .main-content,
    ::deep .e-sidebar ~ .main-content {
        margin-left: 0 !important;
        transform: none !important;
    }

    /* Enable backdrop on mobile - force Over type behavior */
    ::deep .e-sidebar-overlay {
        display: block !important;
        background-color: rgba(0, 0, 0, 0.5) !important;
        opacity: 1 !important;
        pointer-events: auto !important;
        visibility: visible !important;
        z-index: 1000 !important;
        position: fixed !important;
        top: 0 !important;
        left: 0 !important;
        width: 100% !important;
        height: 100% !important;
        transition: opacity 0.3s ease !important;
    }

    /* Hide backdrop when sidebar is closed */
    ::deep .e-sidebar:not(.e-open) + .e-sidebar-overlay,
    ::deep .e-sidebar:not(.e-open) ~ .e-sidebar-overlay {
        display: none !important;
        opacity: 0 !important;
    }
}
