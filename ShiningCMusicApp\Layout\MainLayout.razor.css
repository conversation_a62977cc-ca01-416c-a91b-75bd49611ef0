/* Main Layout Structure */
.main-layout {
    height: 100vh;
    overflow: hidden;
}

/* Syncfusion Sidebar Customization */
::deep .e-sidebar {
    background-image: linear-gradient(180deg, rgb(5, 39, 103) 0%, #3a0647 70%);
    z-index: 1000;
}

::deep .e-sidebar .e-sidebar-overlay {
    z-index: 999;
}

/* Sidebar Content */
.sidebar-content {
    height: 100%;
    display: flex;
    flex-direction: column;
    font-family: 'Helvetica Neue', Helvetica, Arial, sans-serif;
}

.sidebar-header {
    background-color: rgba(0,0,0,0.4);
    border-bottom: 1px solid rgba(255,255,255,0.1);
    flex-shrink: 0;
    font-family: 'Helvetica Neue', Helvetica, Arial, sans-serif;
}

.sidebar-nav {
    flex: 1;
    overflow-y: auto;
    padding: 1rem 0;
}

/* Navigation Items */
.nav-item {
    margin-bottom: 0.25rem;
}

.nav-link {
    display: flex;
    align-items: center;
    padding: 0.75rem 1.5rem;
    color: rgba(255,255,255,0.9);
    text-decoration: none;
    transition: all 0.2s ease;
    border-radius: 0;
    font-family: 'Helvetica Neue', Helvetica, Arial, sans-serif;
    font-size: 0.95rem;
    font-weight: 400;
}

.nav-link:hover {
    background-color: rgba(255,255,255,0.1);
    color: white;
    text-decoration: none;
}

.nav-link.active {
    background-color: rgba(255,255,255,0.2);
    color: white;
    font-weight: 500;
}

.nav-link i {
    width: 20px;
    text-align: center;
}

/* Main Content Area */
.main-content {
    height: 100vh;
    display: flex;
    flex-direction: column;
    transition: margin-left 0.3s ease;
}

/* Top Bar */
.top-bar {
    background-color: #f8f9fa;
    border-bottom: 1px solid #dee2e6;
    height: 60px;
    display: flex;
    align-items: center;
    padding: 0 1rem;
    flex-shrink: 0;
    z-index: 100;
}

.menu-toggle {
    background: transparent;
    border: none;
    font-size: 1.5rem;
    color: #495057;
    cursor: pointer;
    padding: 0.75rem;
    border-radius: 6px;
    transition: all 0.2s ease;
    display: flex !important;
    align-items: center;
    justify-content: center;
    min-width: 44px;
    height: 44px;
    opacity: 0.8;
}

.menu-toggle:hover {
    background-color: rgba(0,0,0,0.08);
    color: #212529;
    opacity: 1;
    transform: scale(1.05);
}

.menu-toggle:focus {
    outline: none;
    box-shadow: 0 0 0 2px rgba(0,123,255,0.25);
    opacity: 1;
}

.menu-toggle:active {
    transform: scale(0.95);
    background-color: rgba(0,0,0,0.12);
}

/* Sidebar Toggle Button - Removed since desktop sidebar is always open */

.user-info {
    display: flex;
    align-items: center;
}

/* Page Content */
.page-content {
    flex: 1;
    overflow-y: auto;
    padding: 1.5rem;
}

/* Desktop Styles */
@media (min-width: 992px) {
    .main-content {
        margin-left: 0; /* Let Syncfusion handle the margin with Push type */
        transition: margin-left 0.3s ease;
    }

    /* Hide mobile toggle on desktop */
    .menu-toggle.d-lg-none {
        display: none !important;
    }

    /* When sidebar is open on desktop with Push type, Syncfusion will handle the margin */
    ::deep .e-sidebar {
        position: fixed !important;
        z-index: 1000;
    }

    /* No backdrop on desktop */
    ::deep .e-sidebar-overlay {
        display: none !important;
    }
}

/* Mobile Styles */
@media (max-width: 991.98px) {
    .main-content {
        margin-left: 0;
    }

    .page-content {
        padding: 1rem;
    }

    /* Mobile toggle button - always visible */
    .menu-toggle.d-lg-none {
        display: flex !important;
    }

    ::deep .e-sidebar {
        z-index: 1001;
    }

    /* Show backdrop on mobile */
    ::deep .e-sidebar-overlay {
        display: block !important;
    }
}
