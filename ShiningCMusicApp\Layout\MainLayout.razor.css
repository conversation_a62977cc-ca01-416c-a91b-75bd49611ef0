.page {
    position: relative;
    display: flex;
    flex-direction: column;
}

main {
    flex: 1;
}

.sidebar {
    background-image: linear-gradient(180deg, rgb(5, 39, 103) 0%, #3a0647 70%);
}

.top-row {
    background-color: #f7f7f7;
    border-bottom: 1px solid #d6d5d5;
    justify-content: flex-end;
    height: 3.5rem;
    display: flex;
    align-items: center;
}

    .top-row ::deep a, .top-row ::deep .btn-link {
        white-space: nowrap;
        margin-left: 1.5rem;
        text-decoration: none;
    }

    .top-row ::deep a:hover, .top-row ::deep .btn-link:hover {
        text-decoration: underline;
    }

    .top-row ::deep a:first-child {
        overflow: hidden;
        text-overflow: ellipsis;
    }

/* Mobile styles - handled in app.css now */

.mobile-menu-btn:hover {
    color: #007bff !important;
    text-decoration: none !important;
}

.mobile-menu-btn:focus {
    box-shadow: none !important;
    outline: none !important;
    text-decoration: none !important;
}

.user-info {
    margin-left: auto;
    display: flex;
    align-items: center;
}

@media (max-width: 991.98px) {
    .page {
        flex-direction: column;
    }

    .top-row {
        position: sticky;
        top: 0;
        z-index: 1000;
        background: white;
        border-bottom: 1px solid #dee2e6;
        height: 3.5rem;
        padding-left: 1rem !important;
        padding-right: 1rem !important;
    }

    .top-row ::deep a, .top-row ::deep .btn-link {
        margin-left: 0;
    }

    .content {
        padding-top: 1rem;
        padding-left: 1rem !important;
        padding-right: 1rem !important;
    }

    /* Offcanvas customization */
    .offcanvas {
        width: 280px;
    }

    .offcanvas-body {
        background-image: linear-gradient(180deg, rgb(5, 39, 103) 0%, #3a0647 70%);
        padding: 0;
    }

    .offcanvas-header {
        background-image: linear-gradient(180deg, rgb(5, 39, 103) 0%, #3a0647 70%);
        color: white;
        border-bottom: 1px solid rgba(255,255,255,0.1);
    }

    .offcanvas-title {
        color: white;
        font-size: 1.1rem;
    }

    .btn-close {
        filter: invert(1);
    }
}

@media (min-width: 992px) {
    .page {
        flex-direction: row;
    }

    .sidebar {
        width: 250px;
        height: 100vh;
        position: fixed;
        top: 0;
        left: 0;
        z-index: 1000;
    }

    main {
        margin-left: 250px;
        width: calc(100% - 250px);
        min-height: 100vh;
        display: flex;
        flex-direction: column;
    }

    .top-row {
        position: sticky;
        top: 0;
        z-index: 1;
        background: white;
        border-bottom: 1px solid #dee2e6;
    }

    .top-row.auth ::deep a:first-child {
        flex: 1;
        text-align: right;
        width: 0;
    }

    .top-row, article {
        padding-left: 2rem !important;
        padding-right: 1.5rem !important;
    }

    article.content {
        flex: 1;
        display: flex;
        flex-direction: column;
    }

    /* Hide mobile menu button on desktop */
    .mobile-menu-btn {
        display: none !important;
    }
}
